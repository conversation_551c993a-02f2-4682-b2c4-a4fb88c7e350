import type { AppRouteModule } from '/@/router/types'

const about: AppRouteModule = {
  path: '/about',
  name: 'About',
  component: () => import('/@/views/about/index.vue'),
  meta: {
    title: 'About',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
  },
}

const aboutPrivacy: AppRouteModule = {
  path: '/about/privacy',
  name: 'AboutPrivacy',
  component: () => import('/@/views/about/privacy.vue'),
  meta: {
    title: 'Privacy Policy',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
  },
}

// English language routes
const aboutEn: AppRouteModule = {
  path: '/about/en',
  name: 'AboutEn',
  component: () => import('/@/views/about/index.vue'),
  meta: {
    title: 'About - English',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
    locale: 'en',
  },
}

const aboutEnPrivacy: AppRouteModule = {
  path: '/about/en/privacy',
  name: 'AboutEnPrivacy',
  component: () => import('/@/views/about/privacy.vue'),
  meta: {
    title: 'Privacy Policy - English',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
    locale: 'en',
  },
}

// Korean language routes
const aboutKo: AppRouteModule = {
  path: '/about/ko',
  name: 'AboutKo',
  component: () => import('/@/views/about/index.vue'),
  meta: {
    title: 'About - Korean',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
    locale: 'ko',
  },
}

const aboutKoPrivacy: AppRouteModule = {
  path: '/about/ko/privacy',
  name: 'AboutKoPrivacy',
  component: () => import('/@/views/about/privacy.vue'),
  meta: {
    title: 'Privacy Policy - Korean',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
    locale: 'ko',
  },
}

// Terms of Service routes
const aboutTerms: AppRouteModule = {
  path: '/about/terms',
  name: 'AboutTerms',
  component: () => import('/@/views/about/termsOfUs.vue'),
  meta: {
    title: 'Terms of Service',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
  },
}

const aboutEnTerms: AppRouteModule = {
  path: '/about/en/terms',
  name: 'AboutEnTerms',
  component: () => import('/@/views/about/termsOfUs.vue'),
  meta: {
    title: 'Terms of Service - English',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
    locale: 'en',
  },
}

const aboutKoTerms: AppRouteModule = {
  path: '/about/ko/terms',
  name: 'AboutKoTerms',
  component: () => import('/@/views/about/termsOfUs.vue'),
  meta: {
    title: 'Terms of Service - Korean',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
    locale: 'ko',
  },
}

// About Detail routes
const aboutDetail: AppRouteModule = {
  path: '/about/about-detail',
  name: 'AboutDetail',
  component: () => import('/@/views/about/about.vue'),
  meta: {
    title: 'About Starchex',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
  },
}

const aboutEnDetail: AppRouteModule = {
  path: '/about/en/about-detail',
  name: 'AboutEnDetail',
  component: () => import('/@/views/about/about.vue'),
  meta: {
    title: 'About Starchex - English',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
    locale: 'en',
  },
}

const aboutKoDetail: AppRouteModule = {
  path: '/about/ko/about-detail',
  name: 'AboutKoDetail',
  component: () => import('/@/views/about/about.vue'),
  meta: {
    title: 'About Starchex - Korean',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
    locale: 'ko',
  },
}

// Marketing routes
const aboutMarketing: AppRouteModule = {
  path: '/about/marketing',
  name: 'AboutMarketing',
  component: () => import('/@/views/about/marketing.vue'),
  meta: {
    title: 'Starchex Marketing',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
  },
}

const aboutEnMarketing: AppRouteModule = {
  path: '/about/en/marketing',
  name: 'AboutEnMarketing',
  component: () => import('/@/views/about/marketing.vue'),
  meta: {
    title: 'Starchex Marketing - English',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
    locale: 'en',
  },
}

const aboutKoMarketing: AppRouteModule = {
  path: '/about/ko/marketing',
  name: 'AboutKoMarketing',
  component: () => import('/@/views/about/marketing.vue'),
  meta: {
    title: 'Starchex Marketing - Korean',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
    locale: 'ko',
  },
}

// Help Delete Account routes
const helpDeleteAccount: AppRouteModule = {
  path: '/help_delete_account',
  name: 'HelpDeleteAccount',
  component: () => import('/@/views/about/help-delete-account.vue'),
  meta: {
    title: 'Account Deletion Help',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
  },
}

const helpDeleteAccountEn: AppRouteModule = {
  path: '/about/en/help_delete_account',
  name: 'HelpDeleteAccountEn',
  component: () => import('/@/views/about/help-delete-account.vue'),
  meta: {
    title: 'Account Deletion Help - English',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
    locale: 'en',
  },
}

const helpDeleteAccountKo: AppRouteModule = {
  path: '/about/ko/help_delete_account',
  name: 'HelpDeleteAccountKo',
  component: () => import('/@/views/about/help-delete-account.vue'),
  meta: {
    title: 'Account Deletion Help - Korean',
    ignoreAuth: true,
    hideMenu: true,
    hideBreadcrumb: true,
    locale: 'ko',
  },
}

export default about
export {
  aboutPrivacy,
  aboutEn,
  aboutEnPrivacy,
  aboutKo,
  aboutKoPrivacy,
  aboutTerms,
  aboutEnTerms,
  aboutKoTerms,
  aboutDetail,
  aboutEnDetail,
  aboutKoDetail,
  aboutMarketing,
  aboutEnMarketing,
  aboutKoMarketing,
  helpDeleteAccount,
  helpDeleteAccountEn,
  helpDeleteAccountKo,
}
