<template>
  <div class="help-delete-account-container">
    <div class="container">
      <h1>{{ getTitle() }}</h1>
      <p class="subtitle">{{ getSubtitle() }}</p>

      <!-- English Content -->
      <div v-if="currentLocale === 'en'">
        <div class="intro-section">
          <p class="intro-text">
            We understand that sometimes you may need to delete your Starchex account. This guide will walk you through the process step by step. Please note that account deletion is permanent and cannot be undone.
          </p>
        </div>

        <div class="warning-section">
          <h3>⚠️ Important Notice</h3>
          <p>
            Once you delete your account, all your data including profile information, connections, and activity history will be permanently removed and cannot be recovered.
          </p>
        </div>

        <h2>How to Delete Your Account</h2>

        <div class="step-section">
          <h3 data-step="1">Access Account Settings</h3>
          <p>Open the Starchex app and navigate to your profile settings.</p>
          <div class="image-container">
            <img src="/@/assets/images/delete_account/starchex_da_1.jpg" alt="Step 1: Access Account Settings" />
          </div>
        </div>

        <div class="step-section">
          <h3 data-step="2">Find Delete Account Option</h3>
          <p>Scroll down to find the "Delete Account" option in the account settings menu.</p>
          <div class="image-container">
            <img src="/@/assets/images/delete_account/starchex_da_2.jpg" alt="Step 2: Find Delete Account Option" />
          </div>
        </div>

        <div class="step-section">
          <h3 data-step="3">Confirm Deletion</h3>
          <p>Read the warning message carefully and confirm your decision to delete your account permanently.</p>
          <div class="image-container">
            <img src="/@/assets/images/delete_account/starchex_da_3.jpg" alt="Step 3: Confirm Deletion" />
          </div>
        </div>

        <div class="alternative-section">
          <h2>Alternative Options</h2>
          <p>Before deleting your account, consider these alternatives:</p>
          <ul>
            <li>Temporarily deactivate your account instead of permanent deletion</li>
            <li>Update your privacy settings to limit data sharing</li>
            <li>Contact our support team if you have specific concerns</li>
          </ul>
        </div>

        <div class="contact-section">
          <h2>Need Help?</h2>
          <p>If you encounter any issues during the deletion process or have questions, please contact our support team.</p>
        </div>
      </div>

      <!-- Korean Content -->
      <div v-else-if="currentLocale === 'ko'">
        <div class="intro-section">
          <p class="intro-text">
            때로는 Starchex 계정을 삭제해야 할 필요가 있을 수 있습니다. 이 가이드는 단계별로 과정을 안내해드립니다. 계정 삭제는 영구적이며 되돌릴 수 없음을 유의해 주세요.
          </p>
        </div>

        <div class="warning-section">
          <h3>⚠️ 중요 안내</h3>
          <p>
            계정을 삭제하면 프로필 정보, 연결, 활동 기록을 포함한 모든 데이터가 영구적으로 제거되며 복구할 수 없습니다.
          </p>
        </div>

        <h2>계정 삭제 방법</h2>

        <div class="step-section">
          <h3 data-step="1">계정 설정 접근</h3>
          <p>Starchex 앱을 열고 프로필 설정으로 이동합니다.</p>
          <div class="image-container">
            <img src="/@/assets/images/delete_account/starchex_da_1.jpg" alt="1단계: 계정 설정 접근" />
          </div>
        </div>

        <div class="step-section">
          <h3 data-step="2">계정 삭제 옵션 찾기</h3>
          <p>계정 설정 메뉴에서 "계정 삭제" 옵션을 찾기 위해 아래로 스크롤합니다.</p>
          <div class="image-container">
            <img src="/@/assets/images/delete_account/starchex_da_2.jpg" alt="2단계: 계정 삭제 옵션 찾기" />
          </div>
        </div>

        <div class="step-section">
          <h3 data-step="3">삭제 확인</h3>
          <p>경고 메시지를 주의 깊게 읽고 계정을 영구적으로 삭제하겠다는 결정을 확인합니다.</p>
          <div class="image-container">
            <img src="/@/assets/images/delete_account/starchex_da_3.jpg" alt="3단계: 삭제 확인" />
          </div>
        </div>

        <div class="alternative-section">
          <h2>대안 옵션</h2>
          <p>계정을 삭제하기 전에 다음 대안들을 고려해보세요:</p>
          <ul>
            <li>영구 삭제 대신 계정을 일시적으로 비활성화</li>
            <li>데이터 공유를 제한하기 위한 개인정보 설정 업데이트</li>
            <li>특정 우려사항이 있는 경우 지원팀에 문의</li>
          </ul>
        </div>

        <div class="contact-section">
          <h2>도움이 필요하신가요?</h2>
          <p>삭제 과정에서 문제가 발생하거나 질문이 있으시면 지원팀에 문의해 주세요.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted } from 'vue'
  import { useRoute } from 'vue-router'
  import { useLocale } from '/@/locales/useLocale'

  const route = useRoute()
  const { changeLocale } = useLocale()

  // Get current locale from route or meta
  const currentLocale = computed(() => {
    const routeLocale = route.meta?.locale as string
    if (routeLocale) {
      return routeLocale
    }

    // Extract locale from path if available
    const pathSegments = route.path.split('/')
    if (pathSegments.length >= 3 && (pathSegments[2] === 'en' || pathSegments[2] === 'ko')) {
      return pathSegments[2]
    }

    return 'en' // default to English
  })

  // Get localized text
  const getTitle = () => {
    return currentLocale.value === 'ko'
      ? '계정 삭제 도움말'
      : 'Account Deletion Help'
  }

  const getSubtitle = () => {
    return currentLocale.value === 'ko'
      ? 'Starchex 계정을 안전하게 삭제하는 방법'
      : 'How to safely delete your Starchex account'
  }

  // Set locale when component mounts
  onMounted(async () => {
    const locale = currentLocale.value
    if (locale && ['en', 'ko', 'vi'].includes(locale)) {
      await changeLocale(locale as any)
    }
  })
</script>

<style scoped>


  /* Responsive Design */
  @media (max-width: 768px) {
    .help-delete-account-container {
      padding: 15px;
    }

    .container {
      margin: 0;
      margin-top: 15px;
      margin-bottom: 15px;
      padding: 30px 25px;
      border-radius: 12px;
      max-width: none;
      width: 100%;
    }

    h1 {
      font-size: 1.8rem;
    }

    .subtitle {
      font-size: 1rem;
    }

    h2 {
      font-size: 1.4rem;
    }

    h3 {
      font-size: 1.2rem;
    }

    .intro-section,
    .warning-section,
    .step-section,
    .alternative-section,
    .contact-section {
      padding: 25px;
    }

    .image-container img {
      max-width: 280px;
    }
  }

  @media (max-width: 480px) {
    .help-delete-account-container {
      padding: 10px;
    }

    .container {
      margin: 0;
      padding: 25px 20px;
      border-radius: 12px;
    }

    h1 {
      font-size: 1.6rem;
    }

    .subtitle {
      font-size: 0.9rem;
    }

    h2 {
      font-size: 1.3rem;
    }

    h3 {
      font-size: 1.1rem;
    }

    p {
      font-size: 0.9rem;
    }

    .intro-section,
    .warning-section,
    .step-section,
    .alternative-section,
    .contact-section {
      padding: 20px;
    }

    .image-container img {
      max-width: 240px;
    }

    .step-section h3::before {
      width: 28px;
      height: 28px;
      font-size: 0.9rem;
    }
  }

  .help-delete-account-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
      sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    min-height: 100vh;
    padding: 20px;
    box-sizing: border-box;
  }

  .container {
    max-width: 800px;
    width: 100%;
    margin: 0 auto;
    padding: 40px 40px;
    background-color: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border-radius: 16px;
    margin-top: 30px;
    margin-bottom: 30px;
    box-sizing: border-box;
  }

  h1 {
    color: #1890ff;
    margin-bottom: 20px;
    font-size: 2.2rem;
    font-weight: 600;
    line-height: 1.3;
  }

  .subtitle {
    color: #666;
    font-size: 1.2rem;
    margin-bottom: 30px;
    font-style: italic;
    text-align: center;
  }

  .intro-section {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 30px;
    border-left: 4px solid #1890ff;
  }

  .intro-text {
    font-size: 1.1rem;
    line-height: 1.7;
    margin: 0;
    color: #2c3e50;
  }

  .warning-section {
    background: linear-gradient(135deg, #fff2e6 0%, #ffe7cc 100%);
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 30px;
    border-left: 4px solid #ff8c00;
  }

  .warning-section h3 {
    color: #d2691e;
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.3rem;
  }

  h2 {
    color: #262626;
    margin-top: 35px;
    margin-bottom: 20px;
    font-size: 1.6rem;
    font-weight: 500;
    border-bottom: 2px solid #1890ff;
    padding-bottom: 8px;
  }

  h3 {
    color: #1890ff;
    margin-top: 25px;
    margin-bottom: 15px;
    font-size: 1.3rem;
    font-weight: 500;
  }

  p {
    margin-bottom: 18px;
    text-align: left;
    line-height: 1.7;
  }

  .step-section {
    background-color: #fafafa;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    border: 1px solid #e8e8e8;
    position: relative;
    overflow: hidden;
  }

  .step-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #1890ff 0%, #40a9ff 100%);
  }

  .step-section h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #1890ff;
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .step-section h3::before {
    content: attr(data-step);
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    font-weight: 600;
    flex-shrink: 0;
  }

  .image-container {
    margin: 20px 0;
    text-align: center;
    display: flex;
    justify-content: center;
  }

  .image-container img {
    max-width: 320px;
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border: 1px solid #e8e8e8;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
  }

  .image-container img:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.18);
  }

  .alternative-section,
  .contact-section {
    background: linear-gradient(135deg, #f6ffed 0%, #f0f9e8 100%);
    padding: 30px;
    border-radius: 12px;
    margin: 30px 0;
    border-left: 4px solid #52c41a;
    position: relative;
  }

  .alternative-section h2,
  .contact-section h2 {
    margin-top: 0;
    color: #389e0d;
    border-bottom: 2px solid #52c41a;
    padding-bottom: 10px;
  }

  ul {
    padding-left: 0;
    list-style: none;
  }

  li {
    margin-bottom: 16px;
    line-height: 1.6;
    position: relative;
    padding-left: 30px;
  }

  li::before {
    content: '✓';
    position: absolute;
    left: 0;
    top: 0;
    color: #52c41a;
    font-weight: bold;
    font-size: 1.1rem;
  }
</style>
